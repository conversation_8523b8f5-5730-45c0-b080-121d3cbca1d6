#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقارير المتقدم مع دعم PDF والعربية - PySide6
Advanced Reports System with PDF and Arabic Support - PySide6
"""

import os
import json
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_pdf import PdfPages
import seaborn as sns

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFormLayout, Q<PERSON><PERSON><PERSON>, QDialogButtonBox,
    QMessageBox, QFileDialog, QTab<PERSON>idget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, QCheckBox,
    QGridLayout, QFrame, QCalendarWidget
)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QColor, QPixmap, QIcon

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    import arabic_reshaper
    from bidi.algorithm import get_display
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class ReportTemplate:
    """قالب التقرير"""
    id: str
    name: str
    description: str
    report_type: str  # مالي، مخزون، عملاء، مشاريع
    fields: List[str]
    filters: Dict[str, Any]
    chart_types: List[str]
    created_date: str

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


class ArabicPDFGenerator:
    """مولد PDF مع دعم العربية"""

    def __init__(self):
        self.setup_arabic_fonts()

    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # تسجيل خط عربي (يجب توفر ملف الخط)
            font_path = "assets/fonts/NotoSansArabic-Regular.ttf"
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Arabic', font_path))
                self.arabic_font = 'Arabic'
            else:
                self.arabic_font = 'Helvetica'  # خط احتياطي
        except:
            self.arabic_font = 'Helvetica'

    def reshape_arabic_text(self, text: str) -> str:
        """تشكيل النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return text

    def create_arabic_paragraph_style(self, name: str, font_size: int = 12,
                                    alignment: int = 2) -> ParagraphStyle:
        """إنشاء نمط فقرة عربية"""
        return ParagraphStyle(
            name=name,
            fontName=self.arabic_font,
            fontSize=font_size,
            alignment=alignment,  # 2 = محاذاة يمين
            rightIndent=0,
            leftIndent=0,
            spaceAfter=6,
            spaceBefore=6
        )


class FinancialReportGenerator:
    """مولد التقارير المالية"""

    def __init__(self, db_managers: Dict[str, Any]):
        self.db_managers = db_managers
        self.pdf_generator = ArabicPDFGenerator()

    def generate_revenue_report(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """تقرير الإيرادات"""
        try:
            # جمع البيانات من قواعد البيانات المختلفة
            clients_data = self.db_managers['clients'].get_all_clients()

            # حساب الإيرادات
            total_revenue = sum(client.get('total_revenue', 0) for client in clients_data)
            total_orders = sum(client.get('total_orders', 0) for client in clients_data)

            # تحليل الإيرادات حسب الفترة
            revenue_by_month = self._calculate_monthly_revenue(clients_data, start_date, end_date)

            # أفضل العملاء
            top_clients = sorted(clients_data,
                               key=lambda x: x.get('total_revenue', 0),
                               reverse=True)[:10]

            report_data = {
                'title': 'تقرير الإيرادات',
                'period': f"من {start_date} إلى {end_date}",
                'total_revenue': total_revenue,  # د.ل
                'total_orders': total_orders,
                'average_order_value': total_revenue / total_orders if total_orders > 0 else 0,  # د.ل
                'revenue_by_month': revenue_by_month,
                'top_clients': top_clients,
                'generated_date': datetime.now().isoformat()
            }

            return report_data

        except Exception as e:
            print(f"خطأ في إنشاء تقرير الإيرادات: {e}")
            return {}

    def _calculate_monthly_revenue(self, clients_data: List[Dict],
                                 start_date: str, end_date: str) -> Dict[str, float]:
        """حساب الإيرادات الشهرية"""
        # هذه دالة مبسطة - في التطبيق الحقيقي ستحتاج لبيانات تفصيلية أكثر
        monthly_revenue = {}

        start = datetime.fromisoformat(start_date)
        end = datetime.fromisoformat(end_date)

        current = start
        while current <= end:
            month_key = current.strftime("%Y-%m")
            # توزيع الإيرادات بشكل تقريبي على الأشهر
            monthly_revenue[month_key] = sum(
                client.get('total_revenue', 0) / 12
                for client in clients_data
            )

            # الانتقال للشهر التالي
            if current.month == 12:
                current = current.replace(year=current.year + 1, month=1)
            else:
                current = current.replace(month=current.month + 1)

        return monthly_revenue

    def generate_profit_loss_report(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """تقرير الأرباح والخسائر"""
        try:
            # جمع بيانات الإيرادات
            revenue_data = self.generate_revenue_report(start_date, end_date)

            # جمع بيانات التكاليف (من المخزون والمشاريع)
            inventory_data = self.db_managers['inventory'].get_all_inventory_items()

            # حساب التكاليف
            total_costs = sum(
                item.get('current_stock', 0) * item.get('unit_cost', 0)
                for item in inventory_data
            )

            # حساب الأرباح
            total_revenue = revenue_data.get('total_revenue', 0)
            gross_profit = total_revenue - total_costs
            profit_margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0

            report_data = {
                'title': 'تقرير الأرباح والخسائر',
                'period': f"من {start_date} إلى {end_date}",
                'total_revenue': total_revenue,  # د.ل
                'total_costs': total_costs,  # د.ل
                'gross_profit': gross_profit,  # د.ل
                'profit_margin': profit_margin,  # %
                'generated_date': datetime.now().isoformat()
            }

            return report_data

        except Exception as e:
            print(f"خطأ في إنشاء تقرير الأرباح والخسائر: {e}")
            return {}


class InventoryReportGenerator:
    """مولد تقارير المخزون"""

    def __init__(self, db_managers: Dict[str, Any]):
        self.db_managers = db_managers
        self.pdf_generator = ArabicPDFGenerator()

    def generate_stock_status_report(self) -> Dict[str, Any]:
        """تقرير حالة المخزون"""
        try:
            inventory_data = self.db_managers['inventory'].get_all_inventory_items()

            # تصنيف العناصر
            in_stock = [item for item in inventory_data if item.get('current_stock', 0) > item.get('minimum_stock', 0)]
            low_stock = [item for item in inventory_data if 0 < item.get('current_stock', 0) <= item.get('minimum_stock', 0)]
            out_of_stock = [item for item in inventory_data if item.get('current_stock', 0) <= 0]

            # حساب القيم
            total_value = sum(
                item.get('current_stock', 0) * item.get('unit_cost', 0)
                for item in inventory_data
            )

            # تحليل حسب الفئات
            categories = {}
            for item in inventory_data:
                category = item.get('category', 'غير محدد')
                if category not in categories:
                    categories[category] = {
                        'count': 0,
                        'value': 0,
                        'items': []
                    }
                categories[category]['count'] += 1
                categories[category]['value'] += item.get('current_stock', 0) * item.get('unit_cost', 0)
                categories[category]['items'].append(item)

            report_data = {
                'title': 'تقرير حالة المخزون',
                'total_items': len(inventory_data),
                'total_value': total_value,
                'in_stock_count': len(in_stock),
                'low_stock_count': len(low_stock),
                'out_of_stock_count': len(out_of_stock),
                'in_stock_items': in_stock,
                'low_stock_items': low_stock,
                'out_of_stock_items': out_of_stock,
                'categories': categories,
                'generated_date': datetime.now().isoformat()
            }

            return report_data

        except Exception as e:
            print(f"خطأ في إنشاء تقرير المخزون: {e}")
            return {}

    def generate_movement_report(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """تقرير حركة المخزون"""
        try:
            # هذا يتطلب تطبيق نظام تتبع الحركات في قاعدة البيانات
            # للتبسيط، سنعرض تقرير أساسي

            inventory_data = self.db_managers['inventory'].get_all_inventory_items()

            # تحليل الحركات (مبسط)
            movements_summary = {
                'total_items_moved': len(inventory_data),
                'items_added': len([item for item in inventory_data if item.get('current_stock', 0) > 0]),
                'items_removed': len([item for item in inventory_data if item.get('current_stock', 0) <= 0]),
            }

            report_data = {
                'title': 'تقرير حركة المخزون',
                'period': f"من {start_date} إلى {end_date}",
                'movements_summary': movements_summary,
                'generated_date': datetime.now().isoformat()
            }

            return report_data

        except Exception as e:
            print(f"خطأ في إنشاء تقرير حركة المخزون: {e}")
            return {}


class ClientReportGenerator:
    """مولد تقارير العملاء"""

    def __init__(self, db_managers: Dict[str, Any]):
        self.db_managers = db_managers
        self.pdf_generator = ArabicPDFGenerator()

    def generate_client_analysis_report(self) -> Dict[str, Any]:
        """تقرير تحليل العملاء"""
        try:
            clients_data = self.db_managers['clients'].get_all_clients()

            # تحليل العملاء
            total_clients = len(clients_data)
            active_clients = len([c for c in clients_data if c.get('status') == 'نشط'])

            # تحليل حسب الأولوية
            priority_analysis = {}
            for client in clients_data:
                priority = client.get('priority', 'عادي')
                if priority not in priority_analysis:
                    priority_analysis[priority] = 0
                priority_analysis[priority] += 1

            # تحليل حسب المدينة
            city_analysis = {}
            for client in clients_data:
                city = client.get('city', 'غير محدد')
                if city not in city_analysis:
                    city_analysis[city] = 0
                city_analysis[city] += 1

            # أفضل العملاء من حيث الإيرادات
            top_revenue_clients = sorted(
                clients_data,
                key=lambda x: x.get('total_revenue', 0),
                reverse=True
            )[:10]

            # أكثر العملاء طلبات
            top_orders_clients = sorted(
                clients_data,
                key=lambda x: x.get('total_orders', 0),
                reverse=True
            )[:10]

            report_data = {
                'title': 'تقرير تحليل العملاء',
                'total_clients': total_clients,
                'active_clients': active_clients,
                'inactive_clients': total_clients - active_clients,
                'priority_analysis': priority_analysis,
                'city_analysis': city_analysis,
                'top_revenue_clients': top_revenue_clients,
                'top_orders_clients': top_orders_clients,
                'generated_date': datetime.now().isoformat()
            }

            return report_data

        except Exception as e:
            print(f"خطأ في إنشاء تقرير تحليل العملاء: {e}")
            return {}


class AdvancedReportsWidget(QWidget):
    """واجهة التقارير المتقدمة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_managers = {}  # سيتم تمرير مديري قواعد البيانات
        self.report_generators = {}
        self.init_ui()
        self.setup_report_generators()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # شريط الأدوات
        self.create_toolbar(layout)

        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # قائمة التقارير
        self.create_reports_panel(main_splitter)

        # منطقة المعاينة والإعدادات
        self.create_preview_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([300, 700])

        layout.addWidget(main_splitter)

    def setup_report_generators(self):
        """إعداد مولدات التقارير"""
        self.report_generators = {
            'financial': FinancialReportGenerator(self.db_managers),
            'inventory': InventoryReportGenerator(self.db_managers),
            'clients': ClientReportGenerator(self.db_managers)
        }
