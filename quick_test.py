#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للميزات المتقدمة
Quick Test for Advanced Features
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_imports():
    """اختبار استيراد جميع الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    tests = [
        ("PyQt6", "PyQt6.QtWidgets"),
        ("NumPy", "numpy"),
        ("Matplotlib", "matplotlib"),
        ("Pandas", "pandas"),
        ("Trimesh", "trimesh"),
        ("المعاين ثلاثي الأبعاد المحسن", "models.advanced_3d_viewer"),
        ("نظام العملاء المتقدم", "clients.advanced_client_system"),
        ("نظام المخزون المتقدم", "inventory.advanced_inventory_system"),
        ("نظام CNC المتقدم", "cnc.advanced_cnc_system"),
        ("نظام التقارير المتقدم", "reports.advanced_reports_system"),
        ("الأنماط الحديثة", "ui.modern_styles"),
        ("مدير الأيقونات", "ui.icons_manager"),
    ]
    
    results = []
    for name, module in tests:
        try:
            __import__(module)
            print(f"✅ {name}: متاح")
            results.append(True)
        except ImportError as e:
            print(f"❌ {name}: غير متاح - {e}")
            results.append(False)
    
    return all(results)

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n🧪 اختبار الوظائف الأساسية...")
    
    try:
        # اختبار إعدادات الإضاءة
        from models.advanced_3d_viewer import LightingSettings
        lighting = LightingSettings()
        assert lighting.ambient_intensity == 0.3
        print("✅ إعدادات الإضاءة: تعمل بشكل صحيح")
    except Exception as e:
        print(f"❌ إعدادات الإضاءة: {e}")
        return False
    
    try:
        # اختبار إعدادات المواد
        from models.advanced_3d_viewer import MaterialSettings
        material = MaterialSettings()
        assert material.material_type == "خشب"
        print("✅ إعدادات المواد: تعمل بشكل صحيح")
    except Exception as e:
        print(f"❌ إعدادات المواد: {e}")
        return False
    
    try:
        # اختبار بيانات العميل
        from clients.advanced_client_system import Client
        client = Client(
            id="test",
            name="عميل تجريبي",
            company="شركة تجريبية",
            phone="*********",
            email="<EMAIL>",
            address="عنوان تجريبي",
            city="الرياض",
            country="السعودية",
            postal_code="12345",
            tax_number="*********",
            payment_terms=30,
            discount_rate=0.05,
            credit_limit=10000.0,
            notes="عميل تجريبي"
        )
        assert client.name == "عميل تجريبي"
        print("✅ بيانات العميل: تعمل بشكل صحيح")
    except Exception as e:
        print(f"❌ بيانات العميل: {e}")
        return False
    
    try:
        # اختبار عنصر المخزون
        from inventory.advanced_inventory_system import InventoryItem
        item = InventoryItem(
            id="test_item",
            name="عنصر تجريبي",
            category="فئة تجريبية",
            subcategory="فئة فرعية",
            description="وصف تجريبي",
            unit="قطعة",
            current_stock=100.0,
            minimum_stock=10.0,
            maximum_stock=1000.0,
            unit_cost=50.0,
            selling_price=75.0,
            supplier="مورد تجريبي",
            location="موقع تجريبي",
            barcode="*********",
            notes="ملاحظات تجريبية"
        )
        assert item.name == "عنصر تجريبي"
        print("✅ عنصر المخزون: يعمل بشكل صحيح")
    except Exception as e:
        print(f"❌ عنصر المخزون: {e}")
        return False
    
    try:
        # اختبار ماكينة CNC
        from cnc.advanced_cnc_system import CNCMachine
        machine = CNCMachine(
            id="test_cnc",
            name="ماكينة تجريبية",
            model="Test Model",
            manufacturer="Test Manufacturer",
            serial_port="COM1",
            baud_rate=9600,
            work_area_x=100.0,
            work_area_y=100.0,
            work_area_z=50.0,
            spindle_speed_max=5000,
            feed_rate_max=500.0,
            tool_changer=False,
            coolant_system=False
        )
        assert machine.name == "ماكينة تجريبية"
        print("✅ ماكينة CNC: تعمل بشكل صحيح")
    except Exception as e:
        print(f"❌ ماكينة CNC: {e}")
        return False
    
    return True

def test_gui_components():
    """اختبار مكونات واجهة المستخدم"""
    print("\n🖥️ اختبار مكونات واجهة المستخدم...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # إنشاء تطبيق Qt للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار المعاين ثلاثي الأبعاد
        try:
            from models.advanced_3d_viewer import Advanced3DViewer
            viewer = Advanced3DViewer()
            print("✅ المعاين ثلاثي الأبعاد: يمكن إنشاؤه")
        except Exception as e:
            print(f"❌ المعاين ثلاثي الأبعاد: {e}")
        
        # اختبار واجهة إدارة العملاء
        try:
            from clients.advanced_client_system import AdvancedClientManagerWidget
            client_widget = AdvancedClientManagerWidget()
            print("✅ واجهة إدارة العملاء: يمكن إنشاؤها")
        except Exception as e:
            print(f"❌ واجهة إدارة العملاء: {e}")
        
        # اختبار واجهة CNC
        try:
            from cnc.advanced_cnc_system import AdvancedCNCManagerWidget
            cnc_widget = AdvancedCNCManagerWidget()
            print("✅ واجهة إدارة CNC: يمكن إنشاؤها")
        except Exception as e:
            print(f"❌ واجهة إدارة CNC: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار السريع"""
    print("🚀 اختبار سريع للميزات المتقدمة")
    print("=" * 50)
    
    # اختبار الاستيرادات
    imports_ok = test_imports()
    
    # اختبار الوظائف الأساسية
    functionality_ok = test_basic_functionality()
    
    # اختبار مكونات واجهة المستخدم
    gui_ok = test_gui_components()
    
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار:")
    print(f"   الاستيرادات: {'✅ نجح' if imports_ok else '❌ فشل'}")
    print(f"   الوظائف الأساسية: {'✅ نجح' if functionality_ok else '❌ فشل'}")
    print(f"   واجهة المستخدم: {'✅ نجح' if gui_ok else '❌ فشل'}")
    
    if imports_ok and functionality_ok and gui_ok:
        print("\n🎉 جميع الاختبارات نجحت! التطبيق جاهز للتشغيل.")
        print("\nلتشغيل التطبيق:")
        print("python run_advanced_furniture_designer.py")
        return True
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        print("\nللمساعدة:")
        print("1. تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
        print("2. تحقق من وجود جميع ملفات التطبيق")
        print("3. راجع رسائل الخطأ أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
