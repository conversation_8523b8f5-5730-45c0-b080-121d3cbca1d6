#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة العملاء المتقدم - PySide6
Advanced Client Management System - PySide6
"""

import sqlite3
import json
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFormLayout, QDialog, QDialogButtonBox,
    QMessageBox, QFileDialog, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, QCheckBox
)
from PySide6.QtCore import Qt, QD<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, QThread
from PySide6.QtGui import QFont, QColor, QPixmap, QIcon

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class Client:
    """بيانات العميل المحسنة"""
    id: str
    name: str
    company: str
    phone: str
    email: str
    address: str
    city: str
    country: str
    postal_code: str
    tax_number: str
    payment_terms: int  # أيام الدفع
    discount_rate: float  # نسبة الخصم
    credit_limit: float  # حد الائتمان (د.ل)
    notes: str
    created_date: str
    last_contact: str
    total_orders: int = 0
    total_revenue: float = 0.0  # إجمالي الإيرادات (د.ل)
    status: str = "نشط"  # نشط، معلق، محظور
    priority: str = "عادي"  # عالي، عادي، منخفض
    source: str = "مباشر"  # مصدر العميل
    assigned_to: str = ""  # المسؤول عن العميل

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class ClientContact:
    """سجل التواصل مع العميل"""
    id: str
    client_id: str
    contact_type: str  # مكالمة، إيميل، اجتماع، زيارة، واتساب
    subject: str
    description: str
    contact_date: str
    follow_up_date: str
    status: str  # مكتمل، معلق، ملغي
    created_by: str
    attachments: List[str] = None

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if self.attachments is None:
            self.attachments = []


@dataclass
class ClientProject:
    """مشروع العميل"""
    id: str
    client_id: str
    project_name: str
    description: str
    start_date: str
    end_date: str
    budget: float  # الميزانية (د.ل)
    status: str  # مقترح، قيد التنفيذ، مكتمل، ملغي
    progress: int  # نسبة الإنجاز
    assigned_team: List[str] = None

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if self.assigned_team is None:
            self.assigned_team = []


class ClientDatabaseManager:
    """مدير قاعدة بيانات العملاء المحسن"""

    def __init__(self, db_path: str = "clients_advanced.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات المحسنة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول العملاء المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                company TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                city TEXT,
                country TEXT,
                postal_code TEXT,
                tax_number TEXT,
                payment_terms INTEGER DEFAULT 30,
                discount_rate REAL DEFAULT 0.0,
                credit_limit REAL DEFAULT 0.0,
                notes TEXT,
                created_date TEXT,
                last_contact TEXT,
                total_orders INTEGER DEFAULT 0,
                total_revenue REAL DEFAULT 0.0,
                status TEXT DEFAULT 'نشط',
                priority TEXT DEFAULT 'عادي',
                source TEXT DEFAULT 'مباشر',
                assigned_to TEXT
            )
        ''')

        # جدول التواصل مع العملاء المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_contacts (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                contact_type TEXT,
                subject TEXT,
                description TEXT,
                contact_date TEXT,
                follow_up_date TEXT,
                status TEXT,
                created_by TEXT,
                attachments TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # جدول مشاريع العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_projects (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                project_name TEXT,
                description TEXT,
                start_date TEXT,
                end_date TEXT,
                budget REAL,
                status TEXT,
                progress INTEGER DEFAULT 0,
                assigned_team TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # جدول المهام والمتابعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_tasks (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                task_title TEXT,
                task_description TEXT,
                due_date TEXT,
                priority TEXT,
                status TEXT,
                assigned_to TEXT,
                created_date TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # جدول الملفات والمرفقات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_attachments (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                file_name TEXT,
                file_path TEXT,
                file_type TEXT,
                upload_date TEXT,
                uploaded_by TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        conn.commit()
        conn.close()

    def add_client(self, client: Client) -> bool:
        """إضافة عميل جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO clients
                (id, name, company, phone, email, address, city, country, postal_code,
                 tax_number, payment_terms, discount_rate, credit_limit,
                 notes, created_date, last_contact, total_orders, total_revenue,
                 status, priority, source, assigned_to)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                client.id, client.name, client.company, client.phone, client.email,
                client.address, client.city, client.country, client.postal_code,
                client.tax_number, client.payment_terms, client.discount_rate,
                client.credit_limit, client.notes, client.created_date,
                client.last_contact, client.total_orders, client.total_revenue,
                client.status, client.priority, client.source, client.assigned_to
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة العميل: {e}")
            return False

    def get_all_clients(self) -> List[Dict[str, Any]]:
        """الحصول على جميع العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM clients ORDER BY name')
            clients_data = cursor.fetchall()

            clients = []
            columns = [desc[0] for desc in cursor.description]

            for client_data in clients_data:
                client_dict = dict(zip(columns, client_data))
                clients.append(client_dict)

            conn.close()
            return clients

        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")
            return []

    def search_clients(self, search_term: str, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """البحث المتقدم في العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = "SELECT * FROM clients WHERE 1=1"
            params = []

            # البحث النصي
            if search_term:
                search_pattern = f"%{search_term}%"
                query += " AND (name LIKE ? OR company LIKE ? OR phone LIKE ? OR email LIKE ?)"
                params.extend([search_pattern] * 4)

            # تطبيق الفلاتر
            if filters:
                if filters.get('status'):
                    query += " AND status = ?"
                    params.append(filters['status'])

                if filters.get('priority'):
                    query += " AND priority = ?"
                    params.append(filters['priority'])

                if filters.get('city'):
                    query += " AND city = ?"
                    params.append(filters['city'])

            query += " ORDER BY name"

            cursor.execute(query, params)
            clients_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            clients = []
            for client_data in clients_data:
                client_dict = dict(zip(columns, client_data))
                clients.append(client_dict)

            conn.close()
            return clients

        except Exception as e:
            print(f"خطأ في البحث: {e}")
            return []

    def add_client_contact(self, contact: ClientContact) -> bool:
        """إضافة سجل تواصل جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO client_contacts
                (id, client_id, contact_type, subject, description, contact_date,
                 follow_up_date, status, created_by, attachments)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                contact.id, contact.client_id, contact.contact_type, contact.subject,
                contact.description, contact.contact_date, contact.follow_up_date,
                contact.status, contact.created_by, json.dumps(contact.attachments)
            ))

            # تحديث تاريخ آخر تواصل للعميل
            cursor.execute('''
                UPDATE clients SET last_contact = ? WHERE id = ?
            ''', (contact.contact_date, contact.client_id))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة سجل التواصل: {e}")
            return False

    def get_client_contacts(self, client_id: str) -> List[Dict[str, Any]]:
        """الحصول على سجلات التواصل للعميل"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM client_contacts
                WHERE client_id = ?
                ORDER BY contact_date DESC
            ''', (client_id,))

            contacts_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            contacts = []
            for contact_data in contacts_data:
                contact_dict = dict(zip(columns, contact_data))
                # تحويل JSON للمرفقات
                if contact_dict.get('attachments'):
                    contact_dict['attachments'] = json.loads(contact_dict['attachments'])
                contacts.append(contact_dict)

            conn.close()
            return contacts

        except Exception as e:
            print(f"خطأ في تحميل سجلات التواصل: {e}")
            return []

    def update_client_stats(self, client_id: str, order_amount: float = 0):
        """تحديث إحصائيات العميل"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE clients
                SET total_orders = total_orders + 1,
                    total_revenue = total_revenue + ?,
                    last_contact = ?
                WHERE id = ?
            ''', (order_amount, datetime.now().isoformat(), client_id))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في تحديث إحصائيات العميل: {e}")
            return False

    def get_client_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            stats = {}

            # إجمالي العملاء
            cursor.execute("SELECT COUNT(*) FROM clients")
            stats['total_clients'] = cursor.fetchone()[0]

            # العملاء النشطون
            cursor.execute("SELECT COUNT(*) FROM clients WHERE status = 'نشط'")
            stats['active_clients'] = cursor.fetchone()[0]

            # إجمالي الإيرادات
            cursor.execute("SELECT SUM(total_revenue) FROM clients")
            result = cursor.fetchone()[0]
            stats['total_revenue'] = result if result else 0.0

            # متوسط قيمة العميل
            if stats['total_clients'] > 0:
                stats['avg_client_value'] = stats['total_revenue'] / stats['total_clients']
            else:
                stats['avg_client_value'] = 0.0

            # العملاء حسب الأولوية
            cursor.execute("SELECT priority, COUNT(*) FROM clients GROUP BY priority")
            priority_data = cursor.fetchall()
            stats['clients_by_priority'] = dict(priority_data)

            # العملاء حسب المدينة
            cursor.execute("SELECT city, COUNT(*) FROM clients GROUP BY city ORDER BY COUNT(*) DESC LIMIT 5")
            city_data = cursor.fetchall()
            stats['top_cities'] = dict(city_data)

            conn.close()
            return stats

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
            return {}


class AdvancedClientManagerWidget(QWidget):
    """واجهة إدارة العملاء المتقدمة"""

    client_selected = Signal(str)  # إشارة اختيار عميل
    client_updated = Signal()  # إشارة تحديث العميل

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = ClientDatabaseManager()
        self.current_client_id = None
        self.init_ui()
        self.load_clients()
        self.setup_auto_refresh()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # شريط الأدوات العلوي
        self.create_toolbar(layout)

        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # قائمة العملاء
        self.create_clients_list(main_splitter)

        # تفاصيل العميل
        self.create_client_details(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([400, 600])

        layout.addWidget(main_splitter)

        # شريط الحالة
        self.create_status_bar(layout)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # البحث المتقدم
        search_group = QGroupBox("البحث والفلترة")
        search_layout = QHBoxLayout(search_group)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم، الشركة، الهاتف، أو الإيميل...")
        self.search_edit.textChanged.connect(self.search_clients)

        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "نشط", "معلق", "محظور"])
        self.status_filter.currentTextChanged.connect(self.apply_filters)

        self.priority_filter = QComboBox()
        self.priority_filter.addItems(["الكل", "عالي", "عادي", "منخفض"])
        self.priority_filter.currentTextChanged.connect(self.apply_filters)

        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(QLabel("الحالة:"))
        search_layout.addWidget(self.status_filter)
        search_layout.addWidget(QLabel("الأولوية:"))
        search_layout.addWidget(self.priority_filter)

        # أزرار الإدارة
        buttons_group = QGroupBox("الإدارة")
        buttons_layout = QHBoxLayout(buttons_group)

        add_client_btn = QPushButton("إضافة عميل")
        add_client_btn.setIcon(IconsManager.get_standard_icon('add'))
        add_client_btn.clicked.connect(self.add_new_client)

        edit_client_btn = QPushButton("تعديل")
        edit_client_btn.setIcon(IconsManager.get_standard_icon('edit'))
        edit_client_btn.clicked.connect(self.edit_selected_client)

        delete_client_btn = QPushButton("حذف")
        delete_client_btn.setIcon(IconsManager.get_standard_icon('delete'))
        delete_client_btn.clicked.connect(self.delete_selected_client)

        export_btn = QPushButton("تصدير")
        export_btn.setIcon(IconsManager.get_standard_icon('export'))
        export_btn.clicked.connect(self.export_clients)

        buttons_layout.addWidget(add_client_btn)
        buttons_layout.addWidget(edit_client_btn)
        buttons_layout.addWidget(delete_client_btn)
        buttons_layout.addWidget(export_btn)

        toolbar_layout.addWidget(search_group)
        toolbar_layout.addWidget(buttons_group)

        parent_layout.addWidget(toolbar_widget)
