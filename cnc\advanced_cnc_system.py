#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تكامل ماكينات CNC المتقدم - PySide6
Advanced CNC Machine Integration System - PySide6
"""

import os
import json
import uuid
import serial
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFormLayout, QDialog, QDialogButtonBox,
    QMessageBox, QFileDialog, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, Q<PERSON><PERSON><PERSON><PERSON>ox,
    QGridLayout, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Q<PERSON>lainTextEdit
)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread, QObject
from PySide6.QtGui import QFont, QColor, QPixmap, QIcon, QTextCursor

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class CNCMachine:
    """ماكينة CNC"""
    id: str
    name: str
    model: str
    manufacturer: str
    serial_port: str
    baud_rate: int
    work_area_x: float  # منطقة العمل X (مم)
    work_area_y: float  # منطقة العمل Y (مم)
    work_area_z: float  # منطقة العمل Z (مم)
    spindle_speed_max: int  # أقصى سرعة للمغزل
    feed_rate_max: float  # أقصى معدل تغذية
    tool_changer: bool  # مغير الأدوات
    coolant_system: bool  # نظام التبريد
    status: str = "غير متصل"  # متصل، غير متصل، يعمل، متوقف، خطأ
    last_maintenance: str = ""
    notes: str = ""

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())


@dataclass
class CNCJob:
    """مهمة CNC"""
    id: str
    name: str
    machine_id: str
    gcode_file: str
    material: str
    thickness: float
    estimated_time: int  # بالدقائق
    actual_time: int = 0
    status: str = "في الانتظار"  # في الانتظار، قيد التنفيذ، مكتمل، متوقف، خطأ
    progress: float = 0.0  # نسبة الإنجاز
    created_date: str = ""
    started_date: str = ""
    completed_date: str = ""
    notes: str = ""

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class GCodeCommand:
    """أمر G-Code"""
    line_number: int
    command: str
    x: Optional[float] = None
    y: Optional[float] = None
    z: Optional[float] = None
    feed_rate: Optional[float] = None
    spindle_speed: Optional[float] = None
    tool_number: Optional[int] = None
    comment: str = ""


class GCodeParser:
    """محلل G-Code"""

    def __init__(self):
        self.commands = []
        self.total_lines = 0
        self.estimated_time = 0

    def parse_file(self, file_path: str) -> List[GCodeCommand]:
        """تحليل ملف G-Code"""
        commands = []

        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith(';'):
                    continue

                command = self.parse_line(line_num, line)
                if command:
                    commands.append(command)

            self.commands = commands
            self.total_lines = len(commands)
            self.estimate_execution_time()

            return commands

        except Exception as e:
            print(f"خطأ في تحليل ملف G-Code: {e}")
            return []

    def parse_line(self, line_number: int, line: str) -> Optional[GCodeCommand]:
        """تحليل سطر G-Code"""
        try:
            # إزالة التعليقات
            if ';' in line:
                comment_index = line.index(';')
                comment = line[comment_index+1:].strip()
                line = line[:comment_index].strip()
            else:
                comment = ""

            if not line:
                return None

            # تحليل الأوامر
            parts = line.split()
            command = parts[0] if parts else ""

            gcode_cmd = GCodeCommand(
                line_number=line_number,
                command=command,
                comment=comment
            )

            # استخراج المعاملات
            for part in parts[1:]:
                if part.startswith('X'):
                    gcode_cmd.x = float(part[1:])
                elif part.startswith('Y'):
                    gcode_cmd.y = float(part[1:])
                elif part.startswith('Z'):
                    gcode_cmd.z = float(part[1:])
                elif part.startswith('F'):
                    gcode_cmd.feed_rate = float(part[1:])
                elif part.startswith('S'):
                    gcode_cmd.spindle_speed = float(part[1:])
                elif part.startswith('T'):
                    gcode_cmd.tool_number = int(part[1:])

            return gcode_cmd

        except Exception as e:
            print(f"خطأ في تحليل السطر {line_number}: {e}")
            return None

    def estimate_execution_time(self):
        """تقدير وقت التنفيذ"""
        total_time = 0
        current_feed_rate = 1000  # معدل تغذية افتراضي
        last_position = [0, 0, 0]

        for cmd in self.commands:
            if cmd.feed_rate:
                current_feed_rate = cmd.feed_rate

            if cmd.command.startswith('G0') or cmd.command.startswith('G1'):
                # حساب المسافة والوقت
                new_position = [
                    cmd.x if cmd.x is not None else last_position[0],
                    cmd.y if cmd.y is not None else last_position[1],
                    cmd.z if cmd.z is not None else last_position[2]
                ]

                distance = ((new_position[0] - last_position[0])**2 +
                           (new_position[1] - last_position[1])**2 +
                           (new_position[2] - last_position[2])**2)**0.5

                time_for_move = distance / current_feed_rate * 60  # بالثواني
                total_time += time_for_move

                last_position = new_position

        self.estimated_time = int(total_time / 60)  # بالدقائق


class CNCCommunicator(QObject):
    """واجهة التواصل مع ماكينة CNC"""

    status_changed = Signal(str)
    progress_updated = Signal(float)
    message_received = Signal(str)
    error_occurred = Signal(str)

    def __init__(self, machine: CNCMachine):
        super().__init__()
        self.machine = machine
        self.serial_connection = None
        self.is_connected = False
        self.is_running = False
        self.current_job = None

    def connect_to_machine(self) -> bool:
        """الاتصال بالماكينة"""
        try:
            self.serial_connection = serial.Serial(
                port=self.machine.serial_port,
                baudrate=self.machine.baud_rate,
                timeout=1
            )

            self.is_connected = True
            self.status_changed.emit("متصل")
            self.message_received.emit("تم الاتصال بالماكينة بنجاح")
            return True

        except Exception as e:
            self.error_occurred.emit(f"فشل الاتصال: {str(e)}")
            return False

    def disconnect_from_machine(self):
        """قطع الاتصال"""
        if self.serial_connection and self.serial_connection.is_open:
            self.serial_connection.close()

        self.is_connected = False
        self.status_changed.emit("غير متصل")
        self.message_received.emit("تم قطع الاتصال")

    def send_command(self, command: str) -> bool:
        """إرسال أمر للماكينة"""
        if not self.is_connected or not self.serial_connection:
            self.error_occurred.emit("الماكينة غير متصلة")
            return False

        try:
            self.serial_connection.write(f"{command}\n".encode())
            response = self.serial_connection.readline().decode().strip()
            self.message_received.emit(f"أرسل: {command} | استقبل: {response}")
            return True

        except Exception as e:
            self.error_occurred.emit(f"خطأ في الإرسال: {str(e)}")
            return False

    def start_job(self, job: CNCJob, gcode_commands: List[GCodeCommand]):
        """بدء تنفيذ مهمة"""
        if not self.is_connected:
            self.error_occurred.emit("الماكينة غير متصلة")
            return

        self.current_job = job
        self.is_running = True
        self.status_changed.emit("يعمل")

        # تشغيل المهمة في خيط منفصل
        self.job_thread = threading.Thread(target=self._execute_job, args=(gcode_commands,))
        self.job_thread.start()

    def _execute_job(self, commands: List[GCodeCommand]):
        """تنفيذ المهمة"""
        try:
            total_commands = len(commands)

            for i, cmd in enumerate(commands):
                if not self.is_running:
                    break

                # إرسال الأمر
                success = self.send_command(cmd.command)
                if not success:
                    break

                # تحديث التقدم
                progress = (i + 1) / total_commands * 100
                self.progress_updated.emit(progress)

                # انتظار قصير
                time.sleep(0.1)

            if self.is_running:
                self.status_changed.emit("مكتمل")
                self.message_received.emit("تم إنجاز المهمة بنجاح")
            else:
                self.status_changed.emit("متوقف")
                self.message_received.emit("تم إيقاف المهمة")

        except Exception as e:
            self.error_occurred.emit(f"خطأ في تنفيذ المهمة: {str(e)}")
            self.status_changed.emit("خطأ")

        finally:
            self.is_running = False

    def stop_job(self):
        """إيقاف المهمة"""
        self.is_running = False
        self.send_command("M0")  # أمر الإيقاف
        self.status_changed.emit("متوقف")

    def emergency_stop(self):
        """إيقاف طارئ"""
        self.is_running = False
        self.send_command("M112")  # إيقاف طارئ
        self.status_changed.emit("إيقاف طارئ")
        self.error_occurred.emit("تم تفعيل الإيقاف الطارئ")


class AdvancedCNCManagerWidget(QWidget):
    """واجهة إدارة ماكينات CNC المتقدمة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.machines = {}  # قاموس الماكينات
        self.communicators = {}  # قاموس المتصلين
        self.current_machine_id = None
        self.gcode_parser = GCodeParser()
        self.init_ui()
        self.load_machines()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # شريط الأدوات
        self.create_toolbar(layout)

        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # قائمة الماكينات
        self.create_machines_panel(main_splitter)

        # لوحة التحكم
        self.create_control_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([300, 700])

        layout.addWidget(main_splitter)

        # شريط الحالة
        self.create_status_bar(layout)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # أزرار الإدارة
        add_machine_btn = QPushButton("إضافة ماكينة")
        add_machine_btn.setIcon(IconsManager.get_standard_icon('add'))
        add_machine_btn.clicked.connect(self.add_machine)

        load_gcode_btn = QPushButton("تحميل G-Code")
        load_gcode_btn.setIcon(IconsManager.get_standard_icon('open'))
        load_gcode_btn.clicked.connect(self.load_gcode_file)

        emergency_stop_btn = QPushButton("إيقاف طارئ")
        emergency_stop_btn.setIcon(IconsManager.get_standard_icon('stop'))
        emergency_stop_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-weight: bold; }")
        emergency_stop_btn.clicked.connect(self.emergency_stop_all)

        toolbar_layout.addWidget(add_machine_btn)
        toolbar_layout.addWidget(load_gcode_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(emergency_stop_btn)

        parent_layout.addWidget(toolbar_widget)

    def load_machines(self):
        """تحميل الماكينات المحفوظة"""
        # هنا يمكن تحميل الماكينات من ملف أو قاعدة بيانات
        # للتجربة، سنضيف ماكينة افتراضية
        default_machine = CNCMachine(
            id="default_001",
            name="ماكينة CNC الرئيسية",
            model="CNC-3018",
            manufacturer="Generic",
            serial_port="COM3",
            baud_rate=115200,
            work_area_x=300,
            work_area_y=180,
            work_area_z=45,
            spindle_speed_max=10000,
            feed_rate_max=1000,
            tool_changer=False,
            coolant_system=False
        )

        self.machines[default_machine.id] = default_machine
