#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تتبع المخزون المتقدم - PySide6
Advanced Inventory Tracking System - PySide6
"""

import sqlite3
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFormLayout, QDialog, QDialogButtonBox,
    QMessageBox, QFileDialog, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, QCheckBox,
    Q<PERSON>ridLayout, QFrame
)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QColor, QPixmap, QIcon

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class InventoryItem:
    """عنصر المخزون"""
    id: str
    name: str
    category: str
    subcategory: str
    description: str
    unit: str  # وحدة القياس (متر، قطعة، كيلو، إلخ)
    current_stock: float
    minimum_stock: float  # الحد الأدنى للتنبيه
    maximum_stock: float  # الحد الأقصى
    unit_cost: float  # تكلفة الوحدة (د.ل)
    selling_price: float  # سعر البيع (د.ل)
    supplier: str  # المورد
    location: str  # موقع التخزين
    barcode: str  # الباركود
    notes: str
    created_date: str
    last_updated: str
    status: str = "متاح"  # متاح، نفد، معلق

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
        if not self.last_updated:
            self.last_updated = datetime.now().isoformat()


@dataclass
class StockMovement:
    """حركة المخزون"""
    id: str
    item_id: str
    movement_type: str  # دخول، خروج، تعديل، تلف
    quantity: float
    unit_cost: float  # تكلفة الوحدة (د.ل)
    total_cost: float  # التكلفة الإجمالية (د.ل)
    reference_number: str  # رقم المرجع (فاتورة، طلب، إلخ)
    notes: str
    created_date: str
    created_by: str

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class Supplier:
    """المورد"""
    id: str
    name: str
    company: str
    phone: str
    email: str
    address: str
    payment_terms: int
    notes: str
    created_date: str
    status: str = "نشط"

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


class InventoryDatabaseManager:
    """مدير قاعدة بيانات المخزون"""

    def __init__(self, db_path: str = "inventory_advanced.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول عناصر المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_items (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                category TEXT,
                subcategory TEXT,
                description TEXT,
                unit TEXT,
                current_stock REAL DEFAULT 0,
                minimum_stock REAL DEFAULT 0,
                maximum_stock REAL DEFAULT 0,
                unit_cost REAL DEFAULT 0,
                selling_price REAL DEFAULT 0,
                supplier TEXT,
                location TEXT,
                barcode TEXT,
                notes TEXT,
                created_date TEXT,
                last_updated TEXT,
                status TEXT DEFAULT 'متاح'
            )
        ''')

        # جدول حركات المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_movements (
                id TEXT PRIMARY KEY,
                item_id TEXT,
                movement_type TEXT,
                quantity REAL,
                unit_cost REAL,
                total_cost REAL,
                reference_number TEXT,
                notes TEXT,
                created_date TEXT,
                created_by TEXT,
                FOREIGN KEY (item_id) REFERENCES inventory_items (id)
            )
        ''')

        # جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                company TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                payment_terms INTEGER DEFAULT 30,
                notes TEXT,
                created_date TEXT,
                status TEXT DEFAULT 'نشط'
            )
        ''')

        # جدول فئات المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_categories (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                parent_category TEXT,
                description TEXT,
                created_date TEXT
            )
        ''')

        # جدول التنبيهات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_alerts (
                id TEXT PRIMARY KEY,
                item_id TEXT,
                alert_type TEXT,
                message TEXT,
                created_date TEXT,
                is_read INTEGER DEFAULT 0,
                FOREIGN KEY (item_id) REFERENCES inventory_items (id)
            )
        ''')

        # جدول طلبات الشراء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_orders (
                id TEXT PRIMARY KEY,
                supplier_id TEXT,
                order_date TEXT,
                expected_delivery TEXT,
                total_amount REAL,  -- المبلغ الإجمالي (د.ل)
                status TEXT,
                notes TEXT,
                created_date TEXT,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
        ''')

        conn.commit()
        conn.close()

    def add_inventory_item(self, item: InventoryItem) -> bool:
        """إضافة عنصر جديد للمخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO inventory_items
                (id, name, category, subcategory, description, unit, current_stock,
                 minimum_stock, maximum_stock, unit_cost, selling_price, supplier,
                 location, barcode, notes, created_date, last_updated, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                item.id, item.name, item.category, item.subcategory, item.description,
                item.unit, item.current_stock, item.minimum_stock, item.maximum_stock,
                item.unit_cost, item.selling_price, item.supplier, item.location,
                item.barcode, item.notes, item.created_date, item.last_updated, item.status
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة عنصر المخزون: {e}")
            return False

    def get_all_inventory_items(self) -> List[Dict[str, Any]]:
        """الحصول على جميع عناصر المخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM inventory_items ORDER BY name')
            items_data = cursor.fetchall()

            items = []
            columns = [desc[0] for desc in cursor.description]

            for item_data in items_data:
                item_dict = dict(zip(columns, item_data))
                items.append(item_dict)

            conn.close()
            return items

        except Exception as e:
            print(f"خطأ في تحميل عناصر المخزون: {e}")
            return []

    def add_stock_movement(self, movement: StockMovement) -> bool:
        """إضافة حركة مخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إضافة الحركة
            cursor.execute('''
                INSERT INTO stock_movements
                (id, item_id, movement_type, quantity, unit_cost, total_cost,
                 reference_number, notes, created_date, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                movement.id, movement.item_id, movement.movement_type, movement.quantity,
                movement.unit_cost, movement.total_cost, movement.reference_number,
                movement.notes, movement.created_date, movement.created_by
            ))

            # تحديث المخزون الحالي
            if movement.movement_type in ["دخول", "إرجاع"]:
                quantity_change = movement.quantity
            elif movement.movement_type in ["خروج", "تلف", "استهلاك"]:
                quantity_change = -movement.quantity
            else:  # تعديل
                quantity_change = movement.quantity

            cursor.execute('''
                UPDATE inventory_items
                SET current_stock = current_stock + ?,
                    last_updated = ?
                WHERE id = ?
            ''', (quantity_change, datetime.now().isoformat(), movement.item_id))

            # فحص التنبيهات
            self.check_stock_alerts(cursor, movement.item_id)

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة حركة المخزون: {e}")
            return False

    def check_stock_alerts(self, cursor, item_id: str):
        """فحص تنبيهات المخزون"""
        try:
            # الحصول على بيانات العنصر
            cursor.execute('''
                SELECT current_stock, minimum_stock, maximum_stock, name
                FROM inventory_items WHERE id = ?
            ''', (item_id,))

            result = cursor.fetchone()
            if not result:
                return

            current_stock, minimum_stock, maximum_stock, item_name = result

            # فحص المخزون المنخفض
            if current_stock <= minimum_stock:
                alert_message = f"تنبيه: المخزون منخفض للعنصر '{item_name}'. الكمية الحالية: {current_stock}"
                cursor.execute('''
                    INSERT INTO inventory_alerts (id, item_id, alert_type, message, created_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', (str(uuid.uuid4()), item_id, "مخزون منخفض", alert_message, datetime.now().isoformat()))

            # فحص نفاد المخزون
            if current_stock <= 0:
                alert_message = f"تحذير: نفد المخزون للعنصر '{item_name}'"
                cursor.execute('''
                    INSERT INTO inventory_alerts (id, item_id, alert_type, message, created_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', (str(uuid.uuid4()), item_id, "نفاد مخزون", alert_message, datetime.now().isoformat()))

        except Exception as e:
            print(f"خطأ في فحص التنبيهات: {e}")

    def get_low_stock_items(self) -> List[Dict[str, Any]]:
        """الحصول على العناصر ذات المخزون المنخفض"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM inventory_items
                WHERE current_stock <= minimum_stock
                ORDER BY (current_stock / minimum_stock) ASC
            ''')

            items_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            items = []
            for item_data in items_data:
                item_dict = dict(zip(columns, item_data))
                items.append(item_dict)

            conn.close()
            return items

        except Exception as e:
            print(f"خطأ في تحميل العناصر منخفضة المخزون: {e}")
            return []
