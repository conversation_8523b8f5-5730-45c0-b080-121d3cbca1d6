#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🪑 مصمم الأثاث الاحترافي المتقدم - الإصدار 2.1
🚀 ابدأ من هنا - START HERE

Advanced Professional Furniture Designer - Version 2.1
PySide6 + Libyan Currency Support
"""

import sys
import os
import subprocess
from pathlib import Path

def print_welcome():
    """طباعة رسالة الترحيب"""
    print("🪑" + "="*60 + "🪑")
    print("    مصمم الأثاث الاحترافي المتقدم - الإصدار 2.1")
    print("    Advanced Professional Furniture Designer")
    print("    PySide6 + دعم الدينار الليبي (د.ل)")
    print("🪑" + "="*60 + "🪑")
    print()
    print("🆕 الجديد في هذا الإصدار:")
    print("   ✅ الانتقال إلى PySide6 لتحسين الأداء")
    print("   ✅ دعم شامل للدينار الليبي (د.ل)")
    print("   ✅ مثبت متطلبات ذكي")
    print("   ✅ مشغل آمن مع فحص تلقائي")
    print("   ✅ واجهة محسنة مع دعم أفضل للعربية")
    print()

def show_options():
    """عرض خيارات التشغيل"""
    print("🚀 خيارات التشغيل:")
    print("   1. تشغيل آمن (موصى به) - يفحص ويثبت المتطلبات تلقائياً")
    print("   2. تثبيت المتطلبات فقط")
    print("   3. اختبار سريع للنظام")
    print("   4. تشغيل مباشر (للمستخدمين المتقدمين)")
    print("   5. عرض المساعدة والوثائق")
    print("   6. خروج")
    print()

def run_safe_launcher():
    """تشغيل المشغل الآمن"""
    print("🛡️ بدء المشغل الآمن...")
    try:
        result = subprocess.run([sys.executable, "run_safe.py"], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في المشغل الآمن: {e}")
        return False
    except FileNotFoundError:
        print("❌ ملف المشغل الآمن غير موجود")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 بدء تثبيت المتطلبات...")
    try:
        result = subprocess.run([sys.executable, "install_requirements.py"], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False
    except FileNotFoundError:
        print("❌ ملف تثبيت المتطلبات غير موجود")
        return False

def run_quick_test():
    """تشغيل الاختبار السريع"""
    print("🧪 بدء الاختبار السريع...")
    try:
        result = subprocess.run([sys.executable, "run_migration_test.py"], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False
    except FileNotFoundError:
        print("❌ ملف الاختبار غير موجود")
        return False

def run_direct():
    """تشغيل مباشر للتطبيق"""
    print("🚀 بدء التشغيل المباشر...")
    try:
        result = subprocess.run([sys.executable, "run_advanced_furniture_designer.py"], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في التشغيل المباشر: {e}")
        return False
    except FileNotFoundError:
        print("❌ ملف التطبيق الرئيسي غير موجود")
        return False

def show_help():
    """عرض المساعدة"""
    print("📖 المساعدة والوثائق:")
    print()
    print("📁 الملفات المفيدة:")
    
    help_files = [
        ("README_ADVANCED_FEATURES.md", "دليل الميزات المتقدمة الكامل"),
        ("QUICK_START_GUIDE.md", "دليل البدء السريع"),
        ("MIGRATION_SUMMARY.md", "ملخص التحديثات والانتقال"),
        ("requirements_core.txt", "المتطلبات الأساسية"),
        ("requirements.txt", "جميع المتطلبات")
    ]
    
    for filename, description in help_files:
        if os.path.exists(filename):
            print(f"   ✅ {filename} - {description}")
        else:
            print(f"   ❌ {filename} - غير موجود")
    
    print()
    print("🧪 ملفات الاختبار:")
    test_files = [
        ("run_migration_test.py", "اختبار سريع للانتقال"),
        ("test_pyside6_migration.py", "اختبار شامل"),
        ("quick_test.py", "اختبار أساسي")
    ]
    
    for filename, description in test_files:
        if os.path.exists(filename):
            print(f"   ✅ {filename} - {description}")
        else:
            print(f"   ❌ {filename} - غير موجود")
    
    print()
    print("💡 نصائح:")
    print("   • ابدأ دائماً بالخيار 1 (التشغيل الآمن)")
    print("   • إذا واجهت مشاكل، جرب الخيار 3 (الاختبار السريع)")
    print("   • للمساعدة في حل المشاكل، راجع QUICK_START_GUIDE.md")
    print("   • التطبيق يدعم الدينار الليبي (د.ل) بشكل كامل")
    print()

def main():
    """الدالة الرئيسية"""
    print_welcome()
    
    while True:
        show_options()
        
        try:
            choice = input("🔢 اختر رقم الخيار [1]: ").strip()
            if not choice:
                choice = "1"
        except KeyboardInterrupt:
            print("\n\n👋 تم الإنهاء بواسطة المستخدم")
            break
        
        print()
        
        if choice == "1":
            print("🛡️ تم اختيار: التشغيل الآمن")
            if run_safe_launcher():
                print("✅ تم التشغيل بنجاح!")
            else:
                print("❌ فشل التشغيل الآمن")
                print("💡 جرب الخيار 2 لتثبيت المتطلبات أولاً")
        
        elif choice == "2":
            print("📦 تم اختيار: تثبيت المتطلبات")
            if install_requirements():
                print("✅ تم تثبيت المتطلبات بنجاح!")
                print("💡 يمكنك الآن اختيار الخيار 1 للتشغيل")
            else:
                print("❌ فشل تثبيت المتطلبات")
        
        elif choice == "3":
            print("🧪 تم اختيار: الاختبار السريع")
            if run_quick_test():
                print("✅ الاختبار مكتمل!")
            else:
                print("❌ فشل الاختبار")
        
        elif choice == "4":
            print("🚀 تم اختيار: التشغيل المباشر")
            print("⚠️ تحذير: هذا للمستخدمين المتقدمين فقط")
            confirm = input("   هل أنت متأكد؟ [y/N]: ").lower().strip()
            if confirm in ['y', 'yes', 'نعم', 'ن']:
                if run_direct():
                    print("✅ تم التشغيل بنجاح!")
                else:
                    print("❌ فشل التشغيل المباشر")
                    print("💡 جرب الخيار 1 للتشغيل الآمن")
            else:
                print("⏭️ تم الإلغاء")
        
        elif choice == "5":
            print("📖 تم اختيار: المساعدة والوثائق")
            show_help()
        
        elif choice == "6":
            print("👋 شكراً لاستخدام مصمم الأثاث الاحترافي!")
            break
        
        else:
            print("❌ خيار غير صحيح، يرجى اختيار رقم من 1 إلى 6")
        
        print("\n" + "-"*60 + "\n")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم الإنهاء بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("💡 يرجى الإبلاغ عن هذا الخطأ")
    
    input("\n⏸️ اضغط Enter للخروج...")
    sys.exit(0)
